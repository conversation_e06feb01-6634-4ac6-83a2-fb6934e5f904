import React from "react";
import { <PERSON> } from "react-router-dom";
import { Activity, Phone, UserPlus, ArrowRight, Heart, Shield, Clock } from "lucide-react";

const LandingPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-center">
            <Activity className="h-10 w-10 text-blue-700 mr-3" />
            <span className="text-3xl font-bold text-blue-700">MedCare Hospital</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-800 mb-6">
            Welcome to <span className="text-blue-700">MedCare</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Your trusted healthcare partner providing exceptional medical services with compassion, 
            expertise, and state-of-the-art facilities for you and your family.
          </p>
          
          {/* Key Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12 max-w-4xl mx-auto">
            <div className="flex flex-col items-center p-6 bg-white rounded-lg shadow-md">
              <Heart className="h-12 w-12 text-red-500 mb-4" />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Expert Care</h3>
              <p className="text-gray-600 text-center">Experienced doctors and medical professionals</p>
            </div>
            <div className="flex flex-col items-center p-6 bg-white rounded-lg shadow-md">
              <Shield className="h-12 w-12 text-green-500 mb-4" />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Safe & Secure</h3>
              <p className="text-gray-600 text-center">Advanced safety protocols and modern facilities</p>
            </div>
            <div className="flex flex-col items-center p-6 bg-white rounded-lg shadow-md">
              <Clock className="h-12 w-12 text-blue-500 mb-4" />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">24/7 Available</h3>
              <p className="text-gray-600 text-center">Round-the-clock emergency and healthcare services</p>
            </div>
          </div>
        </div>

        {/* Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* Contact Card */}
          <div className="bg-white rounded-xl shadow-lg p-8 border-2 border-transparent hover:border-blue-200 transition-all duration-300 transform hover:scale-105">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full p-4 w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                <Phone className="h-10 w-10 text-blue-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-800 mb-4">Contact Us</h2>
              <p className="text-gray-600 mb-6">
                Get in touch with our team for inquiries, emergency services, or general information about our healthcare facilities.
              </p>
              <ul className="text-left text-gray-600 mb-8 space-y-2">
                <li>• Emergency Services</li>
                <li>• General Inquiries</li>
                <li>• Department Information</li>
                <li>• Location & Directions</li>
              </ul>
              <Link
                to="/contact"
                className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold text-lg transition-colors group"
              >
                Contact Now
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>

          {/* Register Card */}
          <div className="bg-white rounded-xl shadow-lg p-8 border-2 border-transparent hover:border-green-200 transition-all duration-300 transform hover:scale-105">
            <div className="text-center">
              <div className="bg-green-100 rounded-full p-4 w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                <UserPlus className="h-10 w-10 text-green-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-800 mb-4">Register as Patient</h2>
              <p className="text-gray-600 mb-6">
                Join our healthcare community and get access to online appointments, medical records, and personalized care services.
              </p>
              <ul className="text-left text-gray-600 mb-8 space-y-2">
                <li>• Online Appointment Booking</li>
                <li>• Medical History Access</li>
                <li>• Doctor Consultations</li>
                <li>• Health Monitoring</li>
              </ul>
              <Link
                to="/register"
                className="inline-flex items-center bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold text-lg transition-colors group"
              >
                Register Now
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-gray-600 mb-6">
            Already registered? 
            <Link to="/home" className="text-blue-600 hover:text-blue-700 font-semibold ml-2">
              Go to Main Website →
            </Link>
          </p>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8 mt-16">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center mb-4">
            <Activity className="h-6 w-6 text-blue-400 mr-2" />
            <span className="text-xl font-bold">MedCare Hospital</span>
          </div>
          <p className="text-gray-400">
            Quality Healthcare for Everyone • Available 24/7
          </p>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
